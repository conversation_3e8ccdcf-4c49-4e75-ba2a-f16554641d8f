'use client';

import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { useZero } from '~/hooks/use-zero';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@kit/ui/card';
import { Badge } from '@kit/ui/badge';
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { Trans } from '@kit/ui/trans';

interface BrandGenerationStatusProps {
  companyId: string;
}

export function BrandGenerationStatus({ companyId }: BrandGenerationStatusProps) {
  const zero = useZero();

  // Query brand generation status using Zero Sync for real-time updates
  const [brandRecords] = useZeroQuery(
    zero?.query.company_brand
      .where('company_id', companyId)
      .orderBy('created_at', 'desc')
      .limit(1) ?? null
  );

  const brandRecord = brandRecords?.[0];

  if (!brandRecord) {
    return null; // No brand generation in progress
  }

  const renderStatus = () => {
    if (brandRecord.error_generating) {
      return (
        <div className="flex items-center space-x-2">
          <AlertCircle className="h-4 w-4 text-destructive" />
          <Badge variant="destructive">
            <Trans i18nKey="onboarding:brandGeneration.error" defaults="Generation Failed" />
          </Badge>
        </div>
      );
    }

    if (brandRecord.is_generating) {
      return (
        <div className="flex items-center space-x-2">
          <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
          <Badge variant="secondary">
            <Trans i18nKey="onboarding:brandGeneration.inProgress" defaults="Generating..." />
          </Badge>
        </div>
      );
    }

    if (brandRecord.brand_profile || brandRecord.messaging_strategy || brandRecord.visual_identity) {
      return (
        <div className="flex items-center space-x-2">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <Badge variant="default" className="bg-green-100 text-green-800">
            <Trans i18nKey="onboarding:brandGeneration.complete" defaults="Complete" />
          </Badge>
        </div>
      );
    }

    return null;
  };

  const getDescription = () => {
    if (brandRecord.error_generating) {
      return (
        <Trans 
          i18nKey="onboarding:brandGeneration.errorDescription" 
          defaults="Brand generation encountered an error. You can try again later from the Brand page." 
        />
      );
    }

    if (brandRecord.is_generating) {
      return (
        <Trans 
          i18nKey="onboarding:brandGeneration.inProgressDescription" 
          defaults="We're analyzing your website and generating your brand profile. This may take a few minutes." 
        />
      );
    }

    if (brandRecord.brand_profile || brandRecord.messaging_strategy || brandRecord.visual_identity) {
      return (
        <Trans 
          i18nKey="onboarding:brandGeneration.completeDescription" 
          defaults="Your brand profile has been generated successfully! You can view and customize it in the Brand section." 
        />
      );
    }

    return null;
  };

  return (
    <Card className="mb-6">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">
            <Trans i18nKey="onboarding:brandGeneration.title" defaults="Brand Generation" />
          </CardTitle>
          {renderStatus()}
        </div>
      </CardHeader>
      <CardContent>
        <CardDescription>
          {getDescription()}
        </CardDescription>
        
        {brandRecord.is_generating && (
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-500 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              <Trans 
                i18nKey="onboarding:brandGeneration.progressNote" 
                defaults="This process runs in the background and won't interrupt your onboarding." 
              />
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
