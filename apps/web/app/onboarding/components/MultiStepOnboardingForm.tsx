'use client';

import { useState, useEffect, useRef, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'sonner';

import {
  type OnboardingFormData,
  defaultValues,
  onboardingSchema,
} from '../schemas/onboarding';
import { WelcomeScreen } from './WelcomeScreen';
import { OnboardingBillingStep } from './OnboardingBillingStep';
import { BrandGenerationStatus } from './BrandGenerationStatus';
import { useUser } from '@kit/supabase/hooks/use-user';
import { checkUserInvitations, completeOnboardingAction } from '../_lib/server-actions';

import pathsConfig from '~/config/paths.config';

enum OnboardingStep {
  TEAM_CREATION = 'team_creation',
  BILLING = 'billing',
  COMPLETE = 'complete',
}

interface TeamAccountData {
  id: string;
  slug: string;
  name: string;
}

export function MultiStepOnboardingForm() {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [currentStep, setCurrentStep] = useState<OnboardingStep>(OnboardingStep.TEAM_CREATION);
  const [teamAccount, setTeamAccount] = useState<TeamAccountData | null>(null);
  const [customerId, setCustomerId] = useState<string | null>(null);

  const user = useUser();
  const userEmail = user?.data?.email;

  const methods = useForm<OnboardingFormData>({
    resolver: zodResolver(onboardingSchema),
    defaultValues,
  });

  // Check for invitations when component mounts
  useEffect(() => {
    if (userEmail) {
      checkUserInvitations(userEmail).then((result) => {
        if (result.hasInvitations) {
          console.log('User has pending invitations:', result.invitations);
        }
      }).catch((error) => {
        console.error('Failed to check invitations:', error);
      });
    }
  }, [userEmail]);

  const handleTeamCreation = async () => {
    // Validate and get normalized form data
    const isValid = await methods.trigger();
    if (!isValid) {
      return;
    }

    const formData = methods.getValues();

    startTransition(async () => {
      try {
        const result = await toast.promise(
          completeOnboardingAction(formData),
          {
            loading: 'Creating your team account...',
            success: 'Team account created successfully!',
            error: 'Failed to create team account. Please try again.',
          }
        );

        if (result.teamAccount) {
          setTeamAccount({
            id: result.teamAccount.id,
            slug: result.teamAccount.slug,
            name: result.teamAccount.name,
          });

          // Move to billing step
          setCurrentStep(OnboardingStep.BILLING);
        }
      } catch (error) {
        console.error('❌ Error completing team creation:', error);
      }
    });
  };

  const handleBillingComplete = () => {
    // Billing completed successfully
    setCurrentStep(OnboardingStep.COMPLETE);
    
    // Redirect to the team dashboard
    if (teamAccount?.slug) {
      router.push(`/home/<USER>/`);
    } else {
      router.push(pathsConfig.app.home);
    }
  };

  const handleBillingSkip = () => {
    // User chose to skip billing for now
    // Still redirect to the team dashboard
    if (teamAccount?.slug) {
      router.push(`/home/<USER>/`);
    } else {
      router.push(pathsConfig.app.home);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case OnboardingStep.TEAM_CREATION:
        return (
          <FormProvider {...methods}>
            <WelcomeScreen 
              handleNext={handleTeamCreation} 
              disabled={isPending} 
            />
          </FormProvider>
        );

      case OnboardingStep.BILLING:
        if (!teamAccount) {
          // This shouldn't happen, but handle gracefully
          setCurrentStep(OnboardingStep.TEAM_CREATION);
          return null;
        }
        
        return (
          <div>
            <BrandGenerationStatus companyId={teamAccount.id} />
            <OnboardingBillingStep
              accountId={teamAccount.id}
              accountSlug={teamAccount.slug}
              customerId={customerId}
              onComplete={handleBillingComplete}
              onSkip={handleBillingSkip}
            />
          </div>
        );

      case OnboardingStep.COMPLETE:
        return (
          <div className="flex w-full flex-col">
            <div className="flex flex-1 items-center justify-center p-8 lg:p-12">
              <div className="w-full max-w-4xl text-center">
                <h1 className="text-3xl font-bold tracking-tight">
                  Welcome to your team!
                </h1>
                <p className="mt-2 text-muted-foreground">
                  Your onboarding is complete. Redirecting to your dashboard...
                </p>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="flex w-full flex-col">
      <div className="flex flex-1 items-center justify-center p-8 lg:p-12">
        <div className="w-full max-w-4xl">
          {renderCurrentStep()}
        </div>
      </div>
    </div>
  );
}
