'use client';

import { useState, useTransition } from 'react';
import { useRouter } from 'next/navigation';
import dynamic from 'next/dynamic';

import { PlanPicker } from '@kit/billing-gateway/components';
import { useAppEvents } from '@kit/shared/events';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@kit/ui/card';
import { Button } from '@kit/ui/button';
import { Trans } from '@kit/ui/trans';
import { LoadingOverlay } from '@kit/ui/loading-overlay';

import billingConfig from '~/config/billing.config';
import pathsConfig from '~/config/paths.config';

const EmbeddedCheckout = dynamic(
  async () => {
    const { EmbeddedCheckout } = await import('@kit/billing-gateway/checkout');

    return {
      default: EmbeddedCheckout,
    };
  },
  {
    ssr: false,
  },
);

interface OnboardingBillingStepProps {
  accountId: string;
  accountSlug: string;
  customerId: string | null | undefined;
  onComplete: () => void;
  onSkip?: () => void;
}

export function OnboardingBillingStep({
  accountId,
  accountSlug,
  customerId,
  onComplete,
  onSkip,
}: OnboardingBillingStepProps) {
  const router = useRouter();
  const [pending, startTransition] = useTransition();
  const appEvents = useAppEvents();

  const [checkoutToken, setCheckoutToken] = useState<string | undefined>(
    undefined,
  );

  // If the checkout token is set, render the embedded checkout component
  if (checkoutToken) {
    return (
      <div className="flex w-full flex-col">
        <div className="flex flex-1 items-center justify-center p-8 lg:p-12">
          <div className="w-full max-w-4xl">
            <EmbeddedCheckout
              checkoutToken={checkoutToken}
              provider={billingConfig.provider}
              onClose={() => {
                setCheckoutToken(undefined);
                // When checkout is closed, consider it complete
                onComplete();
              }}
            />
          </div>
        </div>
      </div>
    );
  }

  // only allow trial if the user is not already a customer
  const canStartTrial = !customerId;

  const handlePlanSelection = ({ planId, productId, startWithTrial }: {
    planId: string;
    productId: string;
    startWithTrial?: boolean;
  }) => {
    startTransition(async () => {
      appEvents.emit({
        type: 'checkout.started',
        payload: {
          planId,
          account: accountSlug,
        },
      });

      try {
        // Import the checkout action dynamically to avoid circular dependencies
        const { createTeamAccountCheckoutSession } = await import(
          '../../home/<USER>/billing/_lib/server/server-actions'
        );

        const { checkoutToken } = await createTeamAccountCheckoutSession({
          planId,
          productId,
          slug: accountSlug,
          accountId,
          startWithTrial,
        });

        setCheckoutToken(checkoutToken);
      } catch (error) {
        console.error('Failed to create checkout session:', error);
        // Handle error appropriately
      }
    });
  };

  const handleSkip = () => {
    if (onSkip) {
      onSkip();
    } else {
      // Default behavior: redirect to app home
      router.push(pathsConfig.app.home);
    }
  };

  return (
    <div className="flex w-full flex-col">
      <div className="flex flex-1 items-center justify-center p-8 lg:p-12">
        <div className="w-full max-w-4xl">
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold tracking-tight">
              <Trans i18nKey="onboarding:billing.title" defaults="Choose Your Plan" />
            </h1>
            <p className="mt-2 text-muted-foreground">
              <Trans 
                i18nKey="onboarding:billing.description" 
                defaults="Select a subscription plan to get started with your team account." 
              />
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>
                <Trans i18nKey="billing:subscriptionCardTitle" defaults="Subscription Plans" />
              </CardTitle>
              <CardDescription>
                <Trans 
                  i18nKey="billing:subscriptionCardDescription" 
                  defaults="Choose the plan that best fits your needs." 
                />
              </CardDescription>
            </CardHeader>

            <CardContent>
              {pending && <LoadingOverlay fullPage={false} />}
              
              <PlanPicker
                pending={pending}
                config={billingConfig}
                canStartTrial={canStartTrial}
                onSubmit={handlePlanSelection}
              />

              <div className="mt-6 flex justify-between">
                <Button
                  variant="outline"
                  onClick={handleSkip}
                  disabled={pending}
                >
                  <Trans i18nKey="onboarding:billing.skipForNow" defaults="Skip for now" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
