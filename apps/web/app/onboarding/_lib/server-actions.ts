'use server';

import { createCreateTeamAccountService } from 'node_modules/@kit/team-accounts/src/server/services/create-team-account.service';
import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';
import { onboardingSchema } from '../schemas/onboarding';
import { CreateInitialCampaignResponse } from '~/types/Campaign';

import { z } from 'zod';
import { getUniqueId } from '~/services/utils';

export const completeOnboardingAction = enhanceAction(
  async function (
    data: z.infer<typeof onboardingSchema>,
    user: { id: string }
  ) {
    const ctx = {
      name: 'complete-onboarding-action',
      userId: user.id,
    };
    const logger = await getLogger();
    logger.info(ctx, 'Starting onboarding process...');

    const client = getSupabaseServerClient();
    const teamAccountsApi = createCreateTeamAccountService(client);

    let contentData: any;
    // Create initial content via API route
    const pushServerUrl = process.env.NEXT_PUBLIC_PUSH_SERVER || 'http://localhost:8080';
    const contentResult = await fetch(
      `${pushServerUrl}/scrape?url=${data.companyWebsite}`, 
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    try {
      contentData = await contentResult.json();
    } catch {
      contentData = { error: 'Failed to parse response' };
    }
   
    try {
      // Create team account with company name
      const { data: teamAccount, error: teamError } =
        await teamAccountsApi.createNewOrganizationAccount({
          name: data.companyName,
          userId: user.id,
          website: data.companyWebsite,
          public_data: JSON.stringify({
            cleanedText: contentData?.content || '',
          }),
        });

      if (teamError || !teamAccount) {
        logger.error(ctx, 'Failed to create team account', {
          error: teamError,
        });
        throw new Error('Failed to create team account');
      }

      // Store additional onboarding data in metadata
      const { error: metadataError } = await client.auth.updateUser({
        data: {
          onboarded: true,
          defaultAccountId: teamAccount.id,
          companyWebsite: data.companyWebsite
        },
      });

      if (metadataError) {
        logger.error(ctx, 'Failed to update user metadata', {
          error: metadataError,
        });
        throw new Error('Failed to update user metadata');
      }

      // Trigger async brand generation after successful team creation
      try {
        await triggerAsyncBrandGenerationAction({
          companyId: teamAccount.id,
          companyName: data.companyName,
          websiteUrl: data.companyWebsite,
        });
        logger.info(ctx, 'Successfully triggered async brand generation');
      } catch (brandError) {
        // Log error but don't fail onboarding - brand generation is optional
        logger.error(ctx, 'Failed to trigger brand generation, but continuing onboarding', {
          error: brandError,
        });
      }

      logger.info(ctx, 'Successfully completed onboarding');

      return {
        success: true,
        teamAccount,
        initialCampaign: 'campaignId' in contentData ? contentData : undefined
      };
    } catch (error) {
      logger.error(ctx, 'Onboarding process failed', { error });
      throw error;
    }
  },
  {
    auth: true,
    schema: onboardingSchema,
  }
);

export const checkUserInvitations = enhanceAction(
  async function (email: string) {
    const client = getSupabaseServerClient();
    const logger = await getLogger();
    try {
      const { data: invitations, error } = await client
        .from('invitations')
        .select('*')
        .eq('email', email.toLowerCase())
        // .is('accepted', false);
      if (error) {
        logger.error({ name: 'check-user-invitations' }, 'Failed to check invitations', {
          error,
          email
        });
        throw error;
      }

      return {
        invitations: invitations || [],
        hasInvitations: (invitations || []).length > 0
      };
    } catch (error) {
      logger.error({ name: 'check-user-invitations' }, 'Error checking invitations', {
        error,
        email
      });
      throw error;
    }
  },
  {
    auth: true,
  }
);

// Schema for async brand generation
const asyncBrandGenerationSchema = z.object({
  companyId: z.string().min(1, 'Company ID is required'),
  companyName: z.string().min(1, 'Company name is required'),
  websiteUrl: z.string().url('Valid website URL is required'),
});

export const triggerAsyncBrandGenerationAction = enhanceAction(
  async function (
    data: z.infer<typeof asyncBrandGenerationSchema>,
    user: { id: string }
  ) {
    const ctx = {
      name: 'trigger-async-brand-generation-action',
      userId: user.id,
      companyId: data.companyId,
    };
    const logger = await getLogger();
    logger.info(ctx, 'Starting async brand generation...');

    try {
      // First, scrape the website to get content
      const scrapeResponse = await fetch('/api/scrape-website', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: data.websiteUrl,
          all: true,
        }),
      });

      if (!scrapeResponse.ok) {
        throw new Error(`Failed to scrape website: ${scrapeResponse.status}`);
      }

      const scrapeResult = await scrapeResponse.json();

      // Create a brand record with is_generating flag using Zero Sync
      // This will be handled by the Zero Sync mutator and will update in real-time
      const brandId = getUniqueId();

      // Note: We'll use the Zero Sync mutation pattern that's already established
      // The actual Zero Sync mutation will be triggered from the client side
      // For now, we'll use Supabase directly but this could be enhanced to use Zero Sync
      const client = getSupabaseServerClient();
      const { error: insertError } = await client
        .from('company_brand')
        .insert({
          id: brandId,
          company_id: data.companyId,
          brand_name: data.companyName,
          is_generating: true,
          error_generating: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

      if (insertError) {
        logger.error(ctx, 'Failed to create brand record', { error: insertError });
        throw new Error('Failed to create brand record');
      }

      // Trigger async brand generation by calling the sb-server endpoint
      // This runs in the background and doesn't block the onboarding flow
      const pushServerUrl = process.env.NEXT_PUBLIC_PUSH_SERVER || 'http://localhost:8080';
      fetch(`${pushServerUrl}/generate-brand-async`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          brandId,
          companyId: data.companyId,
          companyName: data.companyName,
          websiteUrl: data.websiteUrl,
          scrapedContent: scrapeResult.text,
          isHTML: true,
        }),
      }).catch((error) => {
        // Log error but don't throw - this is fire-and-forget
        logger.error(ctx, 'Failed to trigger async brand generation', { error });
      });

      logger.info(ctx, 'Successfully triggered async brand generation');

      return {
        success: true,
        brandId,
      };
    } catch (error) {
      logger.error(ctx, 'Async brand generation trigger failed', { error });
      throw error;
    }
  },
  {
    auth: true,
    schema: asyncBrandGenerationSchema,
  }
);
