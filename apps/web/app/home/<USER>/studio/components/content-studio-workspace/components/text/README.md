# Text Content Editor Components

This directory contains the refactored TextContentEditor components, split into smaller, more manageable pieces for better maintainability and readability.

## Component Structure

### Main Component
- **`TextContentEditor.tsx`** - Main orchestrator component that composes all sub-components

### Sub-components (`_components/` folder)
- **`BasicOptions.tsx`** - Handles the basic tab content (topic/description input)
- **`ContentGenerationButton.tsx`** - Manages the generate button and status display
- **`DataQueries.tsx`** - Custom hook for all Zero database queries
- **`CampaignDataInitializer.tsx`** - Custom hook for initializing campaign data
- **`ContentGenerationService.tsx`** - Custom hook for AI content generation logic

### Existing Components
- **`AdvancedOptions.tsx`** - Advanced options tab content (unchanged)
- **`index.ts`** - Exports the main TextContentEditor component

## Benefits of Refactoring

1. **Improved Maintainability** - Each component has a single responsibility
2. **Better Testability** - Smaller components are easier to unit test
3. **Enhanced Reusability** - Sub-components can be reused in other contexts
4. **Cleaner Code** - Main component is now focused on orchestration
5. **Easier Debugging** - Issues can be isolated to specific components

## Data Flow

```
TextContentEditor (Main)
├── useDataQueries (Data fetching)
├── useCampaignDataInitializer (Campaign setup)
├── useContentGenerationService (AI generation)
├── BasicOptions (Basic tab)
├── AdvancedOptions (Advanced tab)
└── ContentGenerationButton (Generation controls)
```

## Usage

The main `TextContentEditor` component maintains the same external API, so no changes are required in parent components. All functionality has been preserved while improving the internal structure.
