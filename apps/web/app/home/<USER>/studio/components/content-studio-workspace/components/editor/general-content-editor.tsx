'use client';
import { Block } from "@blocknote/core";
import "@blocknote/core/fonts/inter.css";
import { BlockNoteView } from "@blocknote/mantine";
import "@blocknote/mantine/style.css";
import { useEffect, useState, useCallback } from "react";
import { updateCompanyContent } from "~/services/company-content";
import { useQuery as useZeroQuery } from '@rocicorp/zero/react';
import { createBlogPostBlocks, createInitialContent, createVideoBlock } from "~/utils/editor-util";
import { useBaseContent, useEditorContent, useImageContent, useVideoContent } from "../../context/ContentStudioContext";
import { useTheme } from "next-themes";
import { en as aiEn } from "@blocknote/xl-ai/locales";
import "@blocknote/xl-ai/style.css"; // add the AI stylesheet
import { createOpenAI } from '@ai-sdk/openai';
import { en } from "@blocknote/core/locales";
import {
  FormattingToolbar,
  FormattingToolbarController,
  getFormattingToolbarItems,
  useCreateBlockNote,
} from "@blocknote/react";
import {
  AIMenuController,
  AIToolbarButton,
  createAIExtension,
  createBlockNoteAIClient,
} from "@blocknote/xl-ai";
import { useZero } from "~/hooks/use-zero";
import { CompanyContent } from "~/types/company-content";
import { z } from "zod";
import { Textarea } from "@kit/ui/textarea";
import { Input } from "@kit/ui/input";
import { defaultContent } from "./default-content";
import { debounce } from "lodash";



export default function GeneralContentEditor({
  companyContent,
  editable = true
}: {
  companyContent: CompanyContent,
  editable?: boolean
}) {
  // Track if this is the initial content load
  const { theme } = useTheme();
  const zero = useZero();
  
  // Track if content has been initialized to prevent unnecessary replacements
  const [isInitialized, setIsInitialized] = useState(false);
  const [lastSavedContent, setLastSavedContent] = useState<string>('');

  // Use Zero Sync Engine with longer TTL to reduce frequent updates
  const [latestCompanyContent] = useZeroQuery(
    zero.query.company_content.where("id", "=", companyContent.id),
    {
      ttl: '1m'
    }
  );

  // Use the latest data from Zero Sync Engine if available, otherwise fall back to prop
  const currentContent = latestCompanyContent?.[0] || companyContent;

  // Create a hash of the content to detect actual changes
  const contentHash = JSON.stringify(currentContent.content_editor_template || currentContent.content || 'default');


  const client = createBlockNoteAIClient({
    apiKey: 'my-secret-token',
    baseURL: '/api/ai/stream-content',
  });

  const model = createOpenAI({
    ...client.getProviderSettings("google")
  })("google/gemini-2.5-pro") // Match the exact model used in API

  const editor = useCreateBlockNote({
    // Register the AI extension
    dictionary: {
      ...en,
      ai: aiEn, // add default translations for the AI extension
    },
    extensions: [
      createAIExtension({
        model
      }),
    ],
  });

  const { setEditor } = useEditorContent();

  // Create debounced update function (saves every 5 seconds)
  const debouncedUpdateContent = useCallback(
    debounce((blocks: Block[]) => {
      const contentString = JSON.stringify(blocks);
      
      // Only update if content has actually changed
      if (contentString === lastSavedContent) return;
      
      // @ts-expect-error - Zero mutator types are not properly inferred
      zero.mutate.company_content.update({
        id: currentContent.id,
        values: {
          content_editor_template: blocks,
        }
      });
      
      setLastSavedContent(contentString);
    }, 3000),
    [currentContent.id, zero, lastSavedContent]
  );

  // Handle initial content setup - only run once or when content actually changes
  useEffect(() => {
    if (!editor || isInitialized) return;
    
    // Only update if content has actually changed
    if (lastSavedContent === contentHash) return;
    
    setEditor(editor);

    async function updateContent() {
      let blocks: Block[];

      // Priority order:
      // 1. Use content_editor_template if it exists (structured blocks)
      // 2. Convert content field to blocks if it exists (text content)
      // 3. Fall back to default content
      if (currentContent.content_editor_template && Array.isArray(currentContent.content_editor_template)) {
        blocks = currentContent.content_editor_template as Block[];
      } else if (currentContent.content && currentContent.content.trim()) {
        // Convert the text content to blocks using the editor's markdown parser
        try {
          blocks = await editor.tryParseMarkdownToBlocks(currentContent.content);
        } catch (error) {
          console.error('Error parsing content to blocks:', error);
          // If parsing fails, create a simple paragraph block with the content
          blocks = await editor.tryParseMarkdownToBlocks(currentContent.content || '');
        }
      } else {
        blocks = defaultContent as Block[];
      }

      editor.replaceBlocks(editor.document, blocks);
      setIsInitialized(true);
      setLastSavedContent(JSON.stringify(blocks));
    }

    updateContent();
  }, [contentHash, editor, setEditor, isInitialized, lastSavedContent, currentContent]);

  // Cleanup debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedUpdateContent.cancel();
    };
  }, [debouncedUpdateContent]);

  return (
    <>
    <BlockNoteView
      editor={editor} 
      editable={editable}
      theme={theme as "light" | "dark"} 
      formattingToolbar={true}
      onChange={() => {
        const blocks = editor.document;
        debouncedUpdateContent(blocks);
      }}
    >
       <AIMenuController />
       <FormattingToolbarWithAI />
    </BlockNoteView>
    </>
  );
}

function FormattingToolbarWithAI() {
  return (
    <FormattingToolbarController
      formattingToolbar={() => (
        <FormattingToolbar>
          {...getFormattingToolbarItems()}
          {/* Add the AI button */}
          <AIToolbarButton />
        </FormattingToolbar>
      )}
    />
  );
}
 