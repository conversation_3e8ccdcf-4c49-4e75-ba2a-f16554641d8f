'use client';

import { useState, useTransition, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "@kit/ui/sonner";
import { Trans } from "@kit/ui/trans";
import { <PERSON><PERSON> } from "@kit/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@kit/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@kit/ui/form";
import { Input } from "@kit/ui/input";
import { Textarea } from "@kit/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@kit/ui/select";
import { X, User, Briefcase, Building2, Globe, Target, Zap, Users, Code, FileText, MessageSquare, Clock, StickyNote, DollarSign, TrendingUp } from "lucide-react";
import { <PERSON>a } from '~/types/persona';
import { use<PERSON>ero } from '~/hooks/use-zero';
import TagInputField from "./tag-input-field";
import MultiSelectField from "./multi-select-field";
import HybridSelectField from "./hybrid-select-field";

const EditPersonaSchema = z.object({
  name: z.string().min(1, 'Persona name is required'),
  role: z.string().min(1, 'Job title/role is required'),
  department: z.string().optional(),
  management_level: z.string().optional(),
  status: z.enum(['Active', 'Inactive', 'Archived']),
  
  // Company details
  industries: z.array(z.string()).optional(),
  company_size: z.string().optional(),
  location: z.string().optional(),
  tech_stack: z.array(z.string()).optional(),
  budget_range: z.string().optional(),
  
  // Behavioral profile
  challenges: z.array(z.string()).optional(),
  goals: z.array(z.string()).optional(),
  decision_authority: z.string().optional(),
  buying_stage: z.string().optional(),
  info_preferences: z.array(z.string()).optional(),
  
  // Content preferences
  content_formats: z.array(z.string()).optional(),
  communication_style: z.string().optional(),
  content_length: z.string().optional(),
  channels: z.array(z.string()).optional(),
  topics: z.array(z.string()).optional(),
});

type EditPersonaFormData = z.infer<typeof EditPersonaSchema>;

interface EditPersonaDialogProps {
  persona: Persona;
  isOpen: boolean;
  onClose: () => void;
}

type FieldKey = keyof Omit<EditPersonaFormData, 'name' | 'role' | 'status'>;

interface FieldDefinition {
  key: FieldKey;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  component: 'TagInput' | 'MultiSelect' | 'Select' | 'Textarea' | 'Input' | 'HybridSelect';
  options?: readonly string[];
  placeholder?: string;
}

const AVAILABLE_FIELDS: FieldDefinition[] = [
  {
    key: 'department',
    label: 'Department',
    icon: Building2,
    description: 'Department or division',
    component: 'Input',
    placeholder: 'e.g., IT, Marketing, Sales'
  },
  {
    key: 'management_level',
    label: 'Management Level',
    icon: User,
    description: 'Seniority level',
    component: 'Select',
    options: ['C-level', 'VP-level', 'Director', 'Manager', 'Individual Contributor'],
    placeholder: 'Select management level'
  },
  {
    key: 'industries',
    label: 'Industries',
    icon: Briefcase,
    description: 'Target industries',
    component: 'TagInput',
    placeholder: 'e.g., Technology, Finance, Healthcare'
  },
  {
    key: 'company_size',
    label: 'Company Size',
    icon: Users,
    description: 'Size of target companies',
    component: 'Select',
    options: ['1-10', '11-50', '51-200', '201-500', '501-1000', '1001-5000', '5001+'],
    placeholder: 'Select company size'
  },
  {
    key: 'location',
    label: 'Location',
    icon: Globe,
    description: 'Geographic location',
    component: 'Input',
    placeholder: 'e.g., North America, Europe, Global'
  },
  {
    key: 'tech_stack',
    label: 'Technology Stack',
    icon: Code,
    description: 'Technologies they use',
    component: 'TagInput',
    placeholder: 'e.g., AWS, React, Salesforce'
  },
  {
    key: 'budget_range',
    label: 'Budget Range',
    icon: DollarSign,
    description: 'Available budget',
    component: 'Input',
    placeholder: 'e.g., $10k-$50k annually'
  },
  {
    key: 'challenges',
    label: 'Primary Challenges',
    icon: Target,
    description: 'Main pain points',
    component: 'TagInput',
    placeholder: 'e.g., Cost management, Scalability'
  },
  {
    key: 'goals',
    label: 'Goals and Objectives',
    icon: TrendingUp,
    description: 'What they want to achieve',
    component: 'TagInput',
    placeholder: 'e.g., Reduce costs, Increase efficiency'
  },
  {
    key: 'decision_authority',
    label: 'Decision-Making Authority',
    icon: Zap,
    description: 'Level of decision power',
    component: 'Select',
    options: ['Final decision maker', 'Significant influence', 'Some influence', 'Researcher only'],
    placeholder: 'Select decision authority'
  },
  {
    key: 'buying_stage',
    label: 'Typical Buying Stage',
    icon: Clock,
    description: 'Where they are in the process',
    component: 'Select',
    options: ['Awareness', 'Consideration', 'Decision', 'Implementation'],
    placeholder: 'Select buying stage'
  },
  {
    key: 'info_preferences',
    label: 'Information Preferences',
    icon: FileText,
    description: 'Preferred information types',
    component: 'TagInput',
    placeholder: 'e.g., Technical details, Case studies'
  },
  {
    key: 'content_formats',
    label: 'Content Formats',
    icon: FileText,
    description: 'Preferred content types',
    component: 'TagInput',
    placeholder: 'e.g., Blog posts, White papers, Videos'
  },
  {
    key: 'communication_style',
    label: 'Communication Style',
    icon: MessageSquare,
    description: 'Preferred communication approach',
    component: 'Select',
    options: ['Technical', 'Business-focused', 'Conversational', 'Formal', 'Visual/Graphics'],
    placeholder: 'Select communication style'
  },
  {
    key: 'content_length',
    label: 'Content Length Preference',
    icon: Clock,
    description: 'Preferred content length',
    component: 'Select',
    options: ['Short-form', 'Medium', 'Long-form/Detailed'],
    placeholder: 'Select content length'
  },
  {
    key: 'channels',
    label: 'Preferred Channels',
    icon: Globe,
    description: 'Communication channels',
    component: 'TagInput',
    placeholder: 'e.g., Email, LinkedIn, Events'
  },
  {
    key: 'topics',
    label: 'Topics of Interest',
    icon: Target,
    description: 'Relevant topics',
    component: 'TagInput',
    placeholder: 'e.g., Digital transformation, Security'
  }
];

export default function EditPersonaDialog({ persona, isOpen, onClose }: EditPersonaDialogProps) {
  const [pending, startTransition] = useTransition();
  const [activeFields, setActiveFields] = useState<Set<FieldKey>>(new Set());
  const zero = useZero();

  const form = useForm<EditPersonaFormData>({
    resolver: zodResolver(EditPersonaSchema),
    defaultValues: {
      name: '',
      role: '',
      status: 'Active' as const,
    },
  });

  // Initialize form with existing persona data
  useEffect(() => {
    if (persona && isOpen) {
      // Set required fields
      form.setValue('name', persona.data?.name || '');
      form.setValue('role', persona.data?.role || '');
      form.setValue('status', persona.data?.status || 'Active');

      // Initialize active fields and form values from existing data
      const newActiveFields = new Set<FieldKey>();
      
      if (persona.data) {
        // Check each field and add to active fields if it has data
        AVAILABLE_FIELDS.forEach(field => {
          const value = persona.data?.[field.key];
          const hasValue = value !== undefined && value !== null && value !== '' && 
              !(Array.isArray(value) && value.length === 0);
          
          if (hasValue) {
            newActiveFields.add(field.key);
            
            // Ensure proper initialization for different field types
            if (field.component === 'TagInput') {
              // Ensure arrays are properly initialized
              const arrayValue = Array.isArray(value) ? value : [];
              form.setValue(field.key, arrayValue);
            } else {
              // For other field types (Select, Input, Textarea)
              form.setValue(field.key, value);
            }
          }
        });
      }

      setActiveFields(newActiveFields);
    }
  }, [persona, isOpen, form]);

  const addField = (fieldKey: FieldKey) => {
    setActiveFields(prev => new Set([...prev, fieldKey]));
    
    // Set default values for the field
    const field = AVAILABLE_FIELDS.find(f => f.key === fieldKey);
    if (field) {
      if (field.component === 'TagInput') {
        form.setValue(fieldKey, []);
      } else if (field.component === 'Select') {
        form.setValue(fieldKey, '');
      } else if (field.component === 'Input' || field.component === 'Textarea') {
        form.setValue(fieldKey, '');
      }
    }
  };

  const removeField = (fieldKey: FieldKey) => {
    setActiveFields(prev => {
      const newSet = new Set(prev);
      newSet.delete(fieldKey);
      return newSet;
    });
    form.setValue(fieldKey, undefined);
  };

  const onSubmit = (data: EditPersonaFormData) => {
    startTransition(async () => {
      try {
        zero.mutate.personas.update({
          id: persona.id,
          data: { ...persona.data, ...data },
        });
        
        toast.success('Persona updated successfully');
        onClose();
      } catch (error) {
        toast.error('Failed to update persona');
        console.error('Error updating persona:', error);
      }
    });
  };

  const renderField = (fieldDef: FieldDefinition) => {
    const { key, label, component, options, placeholder } = fieldDef;

    return (
      <FormField
        key={key}
        name={key}
        render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center justify-between">
              <span>{label}</span>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeField(key)}
                className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
              >
                <X className="h-3 w-3" />
              </Button>
            </FormLabel>
            <FormControl>
              {(() => {
                switch (component) {
                  case 'TagInput':
                    return (
                      <TagInputField
                        value={Array.isArray(field.value) ? field.value : []}
                        onChange={field.onChange}
                        placeholder={placeholder}
                      />
                    );
                  case 'Select':
                    return (
                      <Select value={field.value || ''} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue placeholder={placeholder} />
                        </SelectTrigger>
                        <SelectContent>
                          {options?.map((option) => (
                            <SelectItem key={option} value={option}>
                              {option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    );
                  case 'Input':
                    return (
                      <Input
                        placeholder={placeholder}
                        {...field}
                        value={field.value || ''}
                      />
                    );
                  case 'Textarea':
                    return (
                      <Textarea
                        placeholder={placeholder}
                        className="resize-none"
                        rows={4}
                        {...field}
                        value={field.value || ''}
                      />
                    );
                  default:
                    return null;
                }
              })()}
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  };

  const availableFields = AVAILABLE_FIELDS.filter(field => !activeFields.has(field.key));

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] overflow-y-auto max-w-4xl w-[95vw] max-w-[95vw] sm:w-full sm:max-w-4xl">
        <DialogHeader>
          <DialogTitle>
            <Trans i18nKey="personas:editPersona" defaults="Edit Persona" />
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Required Fields - Always visible */}
            <FormField
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Persona Name *</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Technical Decision Maker" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Job Title/Role *</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., CTO, IT Director" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Active">Active</SelectItem>
                      <SelectItem value="Inactive">Inactive</SelectItem>
                      <SelectItem value="Archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Dynamic Fields */}
            {Array.from(activeFields).map(fieldKey => {
              const fieldDef = AVAILABLE_FIELDS.find(f => f.key === fieldKey);
              return fieldDef ? renderField(fieldDef) : null;
            })}

            {/* Add Field Buttons */}
            {(availableFields.length > 0) && (
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-muted-foreground">Add Fields</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {availableFields.map((field) => {
                    const Icon = field.icon;
                    return (
                      <Button
                        key={field.key}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addField(field.key)}
                        className="h-auto p-3 flex flex-col gap-1 text-left"
                      >
                        <div className="flex items-center gap-2 w-full">
                          <Icon className="h-4 w-4 flex-shrink-0" />
                          <span className="text-xs font-medium truncate">{field.label}</span>
                        </div>
                        <span className="text-xs text-muted-foreground">{field.description}</span>
                      </Button>
                    );
                  })}
                </div>
              </div>
            )}

            <div className="flex justify-end gap-3 pt-6">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={pending}>
                {pending ? 'Updating...' : 'Update Persona'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
} 