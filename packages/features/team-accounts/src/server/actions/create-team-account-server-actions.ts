'use server';

import { redirect } from 'next/navigation';

import { enhanceAction } from '@kit/next/actions';
import { getLogger } from '@kit/shared/logger';
import { getSupabaseServerClient } from '@kit/supabase/server-client';

import { CreateTeamSchema } from '../../schema/create-team.schema';
import { createCreateTeamAccountService } from '../services/create-team-account.service';

export const createTeamAccountAction = enhanceAction(
  async ({ name, website }, user) => {
    const logger = await getLogger();
    const client = getSupabaseServerClient();
    const service = createCreateTeamAccountService(client);

    const ctx = {
      name: 'team-accounts.create',
      userId: user.id,
      accountName: name,
      website: website || '',
    };

    logger.info(ctx, `Creating team account...`);

    let contentData: any = {};
    let scrapedContent = '';

    // Only scrape if website is provided
    if (website && website.trim() !== '') {
      try {
        logger.info(ctx, 'Scraping website content...');
        
        const pushServerUrl = process.env.NEXT_PUBLIC_PUSH_SERVER || 'http://localhost:8080';
        const contentResult = await fetch(
          `${pushServerUrl}/scrape?url=${website}`, 
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );

        if (contentResult.ok) {
          contentData = await contentResult.json();
          scrapedContent = contentData?.content || '';
          logger.info(ctx, 'Website content scraped successfully');
        } else {
          logger.warn(ctx, 'Failed to scrape website content - HTTP error');
        }
      } catch (error) {
        logger.warn(ctx, 'Failed to scrape website content - fetch error', { error });
        // Continue without scraped content
      }
    } else {
      logger.info(ctx, 'No website provided, skipping scraping');
    }

    const { data, error } = await service.createNewOrganizationAccount({
      name,
      userId: user.id,
      website: website || '',
      public_data: JSON.stringify({
        cleanedText: scrapedContent,
      }),
    });

    if (error) {
      logger.error({ ...ctx, error }, `Failed to create team account`);

      return {
        error: true,
      };
    }

    logger.info(ctx, `Team account created`);

    const accountHomePath = '/home/' + data.slug;

    redirect(accountHomePath);
  },
  {
    schema: CreateTeamSchema,
  },
);
